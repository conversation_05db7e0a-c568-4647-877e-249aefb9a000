﻿<UserControl x:Class="Kiva_MIDI.MiscSettings"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Kiva_MIDI"
             mc:Ignorable="d" 
             d:DesignHeight="350" d:DesignWidth="600">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition/>
            <ColumnDefinition/>
        </Grid.ColumnDefinitions>
        <Border Padding="10">
            <StackPanel>
                <Label FontSize="16">Info Card Values</Label>
                <local:BetterCheckbox CheckToggled="cardLabel_CheckToggled" x:Name="timeLabel" Text="Time"/>
                <local:BetterCheckbox CheckToggled="cardLabel_CheckToggled" x:Name="fpsLabel" Text="FPS"/>
                <DockPanel HorizontalAlignment="Left">
                    <local:BetterCheckbox CheckToggled="cardLabel_CheckToggled" x:Name="estimatedFpsLabel" Text="Estimated FPS"/>
                    <local:InfoIcon Margin="5,1,0,0" Title="Estimated FPS" Text="Due to Kiva's multithreading, unlocking the FPS can make Kiva to fight for resources with all other programs causing stutter. This option allows Kiva to estimate what FPS you would have *if it was unlocked* while it's locked."/>
                </DockPanel>
                <local:BetterCheckbox CheckToggled="cardLabel_CheckToggled" x:Name="ncLabel" Text="Note Count"/>
                <local:BetterCheckbox CheckToggled="cardLabel_CheckToggled" x:Name="npsLabel" Text="NPS"/>
                <local:BetterCheckbox CheckToggled="cardLabel_CheckToggled" x:Name="renderedNotesLabel" Text="Rendered Notes"/>
                <local:BetterCheckbox CheckToggled="cardLabel_CheckToggled" x:Name="polyphonyLabel" Text="Polyphony"/>
                <DockPanel HorizontalAlignment="Left">
                    <local:BetterCheckbox CheckToggled="cardLabel_CheckToggled" x:Name="bufferLengthLabel" Text="Audio Buffer Length"/>
                    <local:InfoIcon Margin="5,1,0,0" Title="Audio Buffer Length" Text="Length of the pre-render audio buffer. Doesn't show anything when KDMAPI or WinMM are selected."/>
                </DockPanel>
            </StackPanel>
        </Border>
        <Border Grid.Column="1" Padding="10">
            <StackPanel>
                <DockPanel>
                    <Label FontSize="14" HorizontalAlignment="Left">
                        Background Color
                    </Label>
                    <local:HexColorPicker ValueChanged="BackgroundColor_ValueChanged" UseAlpha="True" x:Name="backgroundColor" HorizontalAlignment="Left" VerticalAlignment="Bottom" Margin="0,0,0,4"/>
                </DockPanel>
                <DockPanel>
                    <Label FontSize="14" HorizontalAlignment="Left">
                        Bar Color
                    </Label>
                    <local:HexColorPicker ValueChanged="BarColor_ValueChanged" x:Name="barColor" HorizontalAlignment="Left" VerticalAlignment="Bottom" Margin="0,0,0,4"/>
                </DockPanel>
                <local:BetterCheckbox x:Name="hideInfoCard" Text="Hide Info Card" Margin="0,5,0,0" CheckToggled="hideInfoCard_CheckToggled"/>
                <DockPanel Margin="0,5,0,0" LastChildFill="False">
                    <local:BetterCheckbox x:Name="windowTopmost" Text="Main Window Topmost" CheckToggled="windowTopmost_CheckToggled"/>
                    <local:InfoIcon Margin="5,0,0,0" Title="Topmost" Text="The Kiva window will always appear above other windows"/>
                </DockPanel>
                <local:BetterCheckbox x:Name="discordRP" Text="Discord Rich Presence" Margin="0,5,0,0" CheckToggled="discordRP_CheckToggled"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>