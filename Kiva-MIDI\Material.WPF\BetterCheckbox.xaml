﻿<UserControl x:Class="Kiva_MIDI.BetterCheckbox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Kiva_MIDI"
             mc:Ignorable="d" Name="root" >
    <DockPanel MouseDown="DockPanel_MouseDown" Background="Transparent">
        <Grid VerticalAlignment="Center">
            <Border Width="16" Height="16" BorderThickness="2" BorderBrush="Black" Background="Transparent" CornerRadius="3">
                <Border Name="checkedBox" Margin="2" CornerRadius="1" Background="Black">
                </Border>
            </Border>
            <Grid Name="rippleBox" Margin="-7">

            </Grid>
        </Grid>
        <TextBlock FontSize="14" Margin="3,0,0,0" Text="{Binding ElementName=root, Path=Text}" VerticalAlignment="Center"/>
    </DockPanel>
</UserControl>
