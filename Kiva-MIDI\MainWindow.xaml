﻿<Window x:Class="Kiva_MIDI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Kiva_MIDI"
        xmlns:i="clr-namespace:System.Windows.Interop;assembly=PresentationCore"
        mc:Ignorable="d"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal" 
        TextOptions.TextRenderingMode="Auto"        
        Name="mainWindow"
        Title="Kiva" WindowStyle="None" Height="700" Width="1200" 
        Loaded="Window_Loaded" SnapsToDevicePixels="True" PreviewKeyDown="MainWindow_PreviewKeyDown"  KeyDown="MainWindow_KeyDown"
        AllowDrop="True" PreviewDrop="MainWindow_PreviewDrop" PreviewDragEnter="MainWindow_PreviewDragEnter" 
        PreviewDragLeave="MainWindow_PreviewDragLeave" WindowStartupLocation="CenterScreen"
        Focusable="True" Closing="MainWindow_Closing" VerticalAlignment="Center">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="ButtonTemplates.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <WindowChrome.WindowChrome>
        <WindowChrome CaptionHeight="{Binding ActualHeight,ElementName=titlebar}"/>
    </WindowChrome.WindowChrome>
    <Grid>
        <DockPanel LastChildFill="True">
            <Border Visibility="{Binding ChromeVisibility, RelativeSource={RelativeSource FindAncestor, AncestorType=Window}}" Background="#0068c9" DockPanel.Dock="Top" Height="69" x:Name="titlebar">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition/>
                        <RowDefinition/>
                    </Grid.RowDefinitions>
                    <!--<Image HorizontalAlignment="Center" Width="40" Height="Auto" Margin="5" Source="i2m.png"/>-->
                    <DockPanel HorizontalAlignment="Right" Margin="0,0,10,23" Grid.RowSpan="2" Width="52" Panel.ZIndex="100">
                        <Button Style="{StaticResource WindowButton}" Focusable="False" Name="ExitButton" Background="Red" Width="20" Height="20" Margin="3" DockPanel.Dock="Right" WindowChrome.IsHitTestVisibleInChrome="True" Click="ExitButton_Click"></Button>
                        <Button Style="{StaticResource WindowButton}" Focusable="False" Name="MinimiseButton" Background="Orange" Width="20" Height="20" Margin="3" DockPanel.Dock="Right" WindowChrome.IsHitTestVisibleInChrome="True" Click="MinimiseButton_Click"></Button>
                    </DockPanel>
                    <DockPanel LastChildFill="False">
                        <Button Name="openButton" PreviewMouseDown="OpenButton_PreviewMouseDown" WindowChrome.IsHitTestVisibleInChrome="True" Width="{Binding ActualHeight, RelativeSource={RelativeSource Self}}" Style="{StaticResource TransparentButtonStyle}" Padding="0" Margin="0,0,0,0">
                            <Image HorizontalAlignment="Center" RenderOptions.BitmapScalingMode="HighQuality" Height="Auto" Margin="7" Source="Material.WPF/open.png"/>
                        </Button>
                        <Button Name="pauseButton" PreviewMouseDown="PauseButton_Click" WindowChrome.IsHitTestVisibleInChrome="True" Width="{Binding ActualHeight, RelativeSource={RelativeSource Self}}" Style="{StaticResource TransparentButtonStyle}" Padding="0" Margin="0,0,0,0">
                            <Image HorizontalAlignment="Center" Height="Auto" Margin="3" Source="Material.WPF/pause.png"/>
                        </Button>
                        <Button Name="playButton" PreviewMouseDown="PlayButton_Click" WindowChrome.IsHitTestVisibleInChrome="True" Width="{Binding ActualHeight, RelativeSource={RelativeSource Self}}" Style="{StaticResource TransparentButtonStyle}" Padding="0" Margin="0,0,0,0">
                            <Image HorizontalAlignment="Center" RenderOptions.BitmapScalingMode="HighQuality" Height="Auto" Margin="3" Source="Material.WPF/play.png"/>
                        </Button>
                        <Button Name="settingsButton" PreviewMouseDown="SettingsButton_PreviewMouseDown" WindowChrome.IsHitTestVisibleInChrome="True" Width="{Binding ActualHeight, RelativeSource={RelativeSource Self}}" Style="{StaticResource TransparentButtonStyle}" Padding="0" Margin="0,0,0,0">
                            <Image HorizontalAlignment="Center" RenderOptions.BitmapScalingMode="HighQuality" Height="Auto" Margin="8" Source="Material.WPF/settings.png"/>
                        </Button>
                        <DockPanel Margin="5,0" WindowChrome.IsHitTestVisibleInChrome="True">
                            <Label FontSize="16" VerticalAlignment="Center" Padding="5,0,5,3" Margin="0,0,5,0">Speed:</Label>
                            <local:ValueSlider ValueChanged="SpeedSlider_ValueChanged" x:Name="speedSlider" Width="220" Minimum="-3" Maximum="3" TrueMin="0.01" TrueMax="100" DecimalPoints="2" VerticalAlignment="Center" WindowChrome.IsHitTestVisibleInChrome="True"/>
                        </DockPanel>
                        <local:InfoIcon Title="Speed" Text="The relative time scale. Higher values mean faster playback."/>
                        <DockPanel Margin="5,0" WindowChrome.IsHitTestVisibleInChrome="True">
                            <Label FontSize="16" VerticalAlignment="Center" Padding="5,0,5,3" Margin="0,0,5,0">Size:</Label>
                            <local:ValueSlider ValueChanged="SizeSlider_ValueChanged" x:Name="sizeSlider" Width="220" Minimum="-4" Maximum="3" TrueMin="0.01" TrueMax="1000" DecimalPoints="2" VerticalAlignment="Center" WindowChrome.IsHitTestVisibleInChrome="True"/>
                        </DockPanel>
                        <local:InfoIcon Title="Height Scale" Text="The 'Size' of the notes. The smaller the size, the faster they fall."/>
                        <Grid>
                            <Label Name="downloadingUpdateLabel" FontSize="16" VerticalAlignment="Center" Margin="10,0,0,0">Downloading update...</Label>
                            <Button Name="updateInstalledButton" PreviewMouseDown="updateInstalledButton_PreviewMouseDown" FontSize="16" VerticalAlignment="Center" Margin="10,0,0,0" WindowChrome.IsHitTestVisibleInChrome="True" Style="{StaticResource TransparentButtonStyle}" Height="25">Update installed! Click here to restart Kiva</Button>
                        </Grid>
                    </DockPanel>
                    <!--<Slider Name="timeSlider" Margin="10,8,10,7" Grid.Row="1" WindowChrome.IsHitTestVisibleInChrome="True" ValueChanged="TimeSlider_ValueChanged" VerticalAlignment="Center"/>-->
                    <local:BetterSlider Focusable="False" x:Name="timeSlider" UserValueChanged="TimeSlider_UserValueChanged" Margin="10,0,10,0" Minimum="0" Maximum="3" Value="1" Grid.Row="1" WindowChrome.IsHitTestVisibleInChrome="True"/>
                </Grid>
            </Border>
            <Grid Name="glContainer" MouseDown="GlContainer_MouseDown">
                <!--<Grid Background="Green" Panel.ZIndex="-100"></Grid>-->
                <Grid HorizontalAlignment="Right" VerticalAlignment="Top" Visibility="Hidden" Name="audioDesyncLabel" Panel.ZIndex="100">
                    <Grid.Background>
                        <SolidColorBrush Color="Black" Opacity=".5"/>
                    </Grid.Background>
                    <Label Name="skipEventsCount" Foreground="Red" >Skipping Audio Events</Label>
                </Grid>
                <Grid Name="infoCard" HorizontalAlignment="Left" VerticalAlignment="Top" Panel.ZIndex="100">
                    <Grid.Background>
                        <SolidColorBrush Color="Black" Opacity=".5"/>
                    </Grid.Background>
                    <StackPanel>
                        <DockPanel Background="Transparent" Cursor="Hand">
                            <Image Source="logo.png" RenderOptions.BitmapScalingMode="HighQuality" Width="30" Height="30" Margin="3" RenderTransformOrigin="0.5,0.5">
                                <Image.RenderTransform>
                                    <RotateTransform x:Name="rotateLogo" Angle="0"/>
                                </Image.RenderTransform>
                            </Image>
                            <Image Source="kiva.png" RenderOptions.BitmapScalingMode="HighQuality" Width="70" Height="30" Margin="3"/>
                            <Label Name="versionLabel" Foreground="White" VerticalAlignment="Bottom" FontSize="16" Padding="2" Margin="0,0,5,0">
                                v1.0
                            </Label>
                        </DockPanel>
                        <Border Padding="5">
                            <StackPanel Name="infoPanel">
                                <StackPanel.Resources>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="White"/>
                                    </Style>
                                </StackPanel.Resources>
                                <DockPanel Name="timePanel">
                                    <TextBlock Margin="0,0,4,0">Time:</TextBlock>
                                    <TextBlock Name="timeLabel" DockPanel.Dock="Right" HorizontalAlignment="Right"></TextBlock>
                                </DockPanel>
                                <DockPanel Name="fpsPanel">
                                    <TextBlock Margin="0,0,4,0">FPS:</TextBlock>
                                    <TextBlock Name="fpsLabel" DockPanel.Dock="Right" HorizontalAlignment="Right"></TextBlock>
                                </DockPanel>
                                <DockPanel Name="fakeFpsPanel">
                                    <TextBlock Margin="0,0,4,0">Estimated FPS:</TextBlock>
                                    <TextBlock Name="fakeFpsLabel" DockPanel.Dock="Right" HorizontalAlignment="Right"></TextBlock>
                                </DockPanel>
                                <DockPanel Name="renderedNotesPanel">
                                    <TextBlock Margin="0,0,4,0">Rendered Notes:</TextBlock>
                                    <TextBlock Name="renderedNotesLabel" DockPanel.Dock="Right" HorizontalAlignment="Right"></TextBlock>
                                </DockPanel>
                                <DockPanel Name="npsPanel">
                                    <TextBlock Margin="0,0,4,0">NPS:</TextBlock>
                                    <TextBlock Name="npsLabelLabel" DockPanel.Dock="Right" HorizontalAlignment="Right"></TextBlock>
                                </DockPanel>
                                <DockPanel Name="polyphonyPanel">
                                    <TextBlock Margin="0,0,4,0">Polyphony:</TextBlock>
                                    <TextBlock Name="polyphonyLabel" DockPanel.Dock="Right" HorizontalAlignment="Right"></TextBlock>
                                </DockPanel>
                                <StackPanel Name="ncPanel">
                                    <DockPanel>
                                        <TextBlock Margin="0,0,4,0">NC:</TextBlock>
                                        <TextBlock Name="ncLabel" DockPanel.Dock="Right" HorizontalAlignment="Right"></TextBlock>
                                    </DockPanel>
                                    <DockPanel>
                                        <TextBlock Margin="0,0,4,0">Passed:</TextBlock>
                                        <TextBlock Name="npLabel" DockPanel.Dock="Right" HorizontalAlignment="Right"></TextBlock>
                                    </DockPanel>
                                </StackPanel>
                                <DockPanel Name="bufferLenPanel">
                                    <TextBlock Margin="0,0,4,0">Audio Buffer:</TextBlock>
                                    <TextBlock Name="bufferLenLabel" DockPanel.Dock="Right" HorizontalAlignment="Right"></TextBlock>
                                </DockPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Grid>
                <local:DXElement x:Name="dx11img"/>
            </Grid>
        </DockPanel>
        <Rectangle Name="dropHighlight" Fill="White" Visibility="Hidden" Opacity="0.3"/>
    </Grid>
</Window>
