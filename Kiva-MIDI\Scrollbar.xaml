﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:<PERSON><PERSON>_MIDI">
    <Style TargetType="{x:Type ScrollBar}">
        <Style.Resources>
            <Style x:Key="PageScrollButton" TargetType="{x:Type RepeatButton}">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type RepeatButton}">
                            <Rectangle Fill="Transparent"/>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="VerticalThumbStyle" TargetType="{x:Type Thumb}">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type Thumb}">
                            <Border Background="Transparent">
                                <Rectangle x:Name="Slider" Fill="#000000" Width="4" HorizontalAlignment="Right" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Trigger.EnterActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation To="12" DecelerationRatio="1" Storyboard.TargetName="Slider" Storyboard.TargetProperty="Width" Duration="0:0:0.10" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.EnterActions>
                                    <Trigger.ExitActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation To="12" DecelerationRatio="1" Storyboard.TargetName="Slider" Storyboard.TargetProperty="Width" Duration="0:0:0.10" />
                                                <DoubleAnimation To="4" AccelerationRatio="1" Storyboard.TargetName="Slider" Storyboard.TargetProperty="Width" BeginTime="0:0:0.15" Duration="0:0:0.25" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.ExitActions>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="HorizontalThumbStyle" TargetType="{x:Type Thumb}">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type Thumb}">
                            <Border Background="Transparent">
                                <Rectangle x:Name="Slider" Fill="#000000" Height="4" VerticalAlignment="Bottom" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Trigger.EnterActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation To="12" DecelerationRatio="1" Storyboard.TargetName="Slider" Storyboard.TargetProperty="Height" Duration="0:0:0.1" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.EnterActions>
                                    <Trigger.ExitActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation To="4" DecelerationRatio="1" Storyboard.TargetName="Slider" Storyboard.TargetProperty="Height" Duration="0:0:0.25" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.ExitActions>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </Style.Resources>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Width" Value="0"/>
        <Setter Property="Margin" Value="-17,0,0,0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollBar}">
                    <Track x:Name="PART_Track" IsDirectionReversed="true" IsEnabled="{TemplateBinding IsMouseOver}">
                        <Track.DecreaseRepeatButton>
                            <RepeatButton Command="{x:Static ScrollBar.PageUpCommand}" Style="{StaticResource PageScrollButton}"/>
                        </Track.DecreaseRepeatButton>
                        <Track.IncreaseRepeatButton>
                            <RepeatButton Command="{x:Static ScrollBar.PageDownCommand}" Style="{StaticResource PageScrollButton}"/>
                        </Track.IncreaseRepeatButton>
                        <Track.Thumb>
                            <Thumb Style="{StaticResource VerticalThumbStyle}" />
                        </Track.Thumb>
                    </Track>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter Property="Width" Value="Auto"/>
                <Setter Property="Height" Value="12"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type ScrollBar}">
                            <Track x:Name="PART_Track" IsEnabled="{TemplateBinding IsMouseOver}">
                                <Track.DecreaseRepeatButton>
                                    <RepeatButton Command="{x:Static ScrollBar.PageLeftCommand}" Style="{StaticResource PageScrollButton}"/>
                                </Track.DecreaseRepeatButton>
                                <Track.IncreaseRepeatButton>
                                    <RepeatButton Command="{x:Static ScrollBar.PageRightCommand}" Style="{StaticResource PageScrollButton}"/>
                                </Track.IncreaseRepeatButton>
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource HorizontalThumbStyle}" />
                                </Track.Thumb>
                            </Track>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>