﻿<UserControl x:Class="Kiva_MIDI.AudioSettings"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Kiva_MIDI"
             mc:Ignorable="d" 
             d:DesignHeight="350" d:DesignWidth="600">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../ButtonTemplates.xaml"/>
                <ResourceDictionary Source="../Scrollbar.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <DockPanel LastChildFill="True">
        <Grid DockPanel.Dock="Top">
            <DockPanel HorizontalAlignment="Center">
                <Label FontSize="16">
                    Rendering Engine:
                </Label>
                <Button Name="kdmapiEngine" FontSize="16" Width="120" Style="{DynamicResource TransparentButtonStyle}">
                    KDMAPI
                </Button>
                <Button Name="winmmEngine" FontSize="16" Width="120" Style="{DynamicResource TransparentButtonStyle}">
                    WinMM
                </Button>
                <Button Name="prerenderEngine" FontSize="16" Width="120" Style="{DynamicResource TransparentButtonStyle}">
                    Pre-Render
                </Button>
            </DockPanel>
        </Grid>
        <Grid>
            <local:KDMAPIAudioSettings x:Name="kdmapiSettings"/>
            <local:WinMMAudioSettings x:Name="winmmSettings"/>
            <local:PreRenderAudioSettings x:Name="prerenderSettings"/>
        </Grid>
    </DockPanel>
</UserControl>
