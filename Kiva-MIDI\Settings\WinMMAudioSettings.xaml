﻿<UserControl x:Class="Kiva_MIDI.WinMMAudioSettings"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Kiva_MIDI"
             mc:Ignorable="d" d:DesignHeight="254" d:DesignWidth="452">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../ButtonTemplates.xaml"/>
                <ResourceDictionary Source="../Scrollbar.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <DockPanel>
            <Label DockPanel.Dock="Top">MIDI Output Device</Label>
            <Border BorderBrush="Black" BorderThickness="0">
                <ScrollViewer>
                    <StackPanel Name="devicesList" Background="Transparent">

                    </StackPanel>
                </ScrollViewer>
            </Border>
        </DockPanel>
    </Grid>
</UserControl>