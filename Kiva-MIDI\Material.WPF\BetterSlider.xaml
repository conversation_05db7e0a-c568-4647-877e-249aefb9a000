﻿<UserControl x:Class="Kiva_MIDI.BetterSlider"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Kiva_MIDI"
             mc:Ignorable="d" 
             Name="slider"
             d:DesignHeight="30" d:DesignWidth="800" Focusable="True">
    <UserControl.Resources>
        <local:DoubleMultiplyConverter x:Key="DoubleMultiplyConverter"/>
        <local:ThicknessConverter x:Key="ThicknessConverter"/>
    </UserControl.Resources>
    <Grid Height="14" Name="clickerGrid" Background="Transparent" MouseEnter="ClickerGrid_MouseEnter" MouseLeave="ClickerGrid_MouseLeave" MouseDown="ClickerGrid_MouseDown" MouseMove="ClickerGrid_MouseMove" MouseUp="ClickerGrid_MouseUp">
        <Grid Margin="5" VerticalAlignment="Center" Name="barGrid" Height="4" Background="#55000000">
            <Rectangle HorizontalAlignment="Left" Fill="#AA000000">
                <Rectangle.Width>
                    <Binding ElementName="slider" Path="ScaledValue"/>
                </Rectangle.Width>
            </Rectangle>
        </Grid>
        <Grid Name="headGrid" HorizontalAlignment="Left" VerticalAlignment="Center" Height="10" Width="10">
            <Ellipse Name="trueHead" Width="10" Height="10" HorizontalAlignment="Center" VerticalAlignment="Center" Fill="Black"/>
            <Grid Margin="-7" Name="auraGrid">
                <Ellipse Name="hoverEllipse" Visibility="Hidden" Fill="#44000000"/>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
