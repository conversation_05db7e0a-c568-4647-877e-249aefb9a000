﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Ki<PERSON>_MIDI">
    <Style x:Key="WindowButton" TargetType="Button">
        <Setter Property="OverridesDefaultStyle" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Name="border"
                        BorderThickness="1"
                        Padding="4,2"
                        BorderBrush="DarkGray"
                        CornerRadius="3"
                        Background="{TemplateBinding Background}">
                        <Grid >
                            <ContentPresenter HorizontalAlignment="Center"
                           VerticalAlignment="Center" Name="content"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="#FF4788c8" />
                            <Setter Property="Foreground" Value="#FF4788c8" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                        </Trigger>
                        <Trigger Property="IsDefaulted" Value="True">
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="MenuButtonStyle" TargetType="Button">
        <Setter Property="OverridesDefaultStyle" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Name="border"
                        Padding="4,2"
                        Background="{TemplateBinding Background}">
                        <Grid >
                            <ContentPresenter HorizontalAlignment="Center"
                           VerticalAlignment="Center" Name="content"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#44FFFFFF" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                        </Trigger>
                        <Trigger Property="IsDefaulted" Value="True">
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>