﻿using SharpDX.Direct3D9;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Kiva_MIDI
{
    static class DXSharing
    {
        // Wine detection helper
        private static bool IsRunningUnderWine()
        {
            try
            {
                return Environment.GetEnvironmentVariable("WINEPREFIX") != null ||
                       Environment.GetEnvironmentVariable("WINEARCH") != null ||
                       System.IO.File.Exists("/proc/version");
            }
            catch
            {
                return false;
            }
        }

        public static Texture GetSharedD3D9(this DeviceEx device, SharpDX.Direct3D11.Texture2D renderTarget)
        {
            if (renderTarget == null)
                return null;

            Format format = ToD3D9(renderTarget.Description.Format);
            if (format == Format.Unknown)
                throw new ArgumentException("Texture format is not compatible with OpenSharedResource");

            // Check if texture has shared flag
            bool isSharedTexture = (renderTarget.Description.OptionFlags & SharpDX.Direct3D11.ResourceOptionFlags.Shared) != 0;

            if (isSharedTexture)
            {
                // Try shared resource first (works on Windows)
                try
                {
                    using (var resource = renderTarget.QueryInterface<SharpDX.DXGI.Resource>())
                    {
                        IntPtr handle = resource.SharedHandle;
                        if (handle == IntPtr.Zero)
                            throw new ArgumentNullException("Handle");
                        return new Texture(device, renderTarget.Description.Width, renderTarget.Description.Height, 1, Usage.RenderTarget, format, Pool.Default, ref handle);
                    }
                }
                catch (SharpDX.SharpDXException ex)
                {
                    // Wine compatibility fallback: Create a separate D3D9 texture
                    // This happens when Wine/DXVK doesn't support VK_KHR_EXTERNAL_MEMORY_WIN32
                    if (IsRunningUnderWine() || ex.Message.Contains("shared resource") || ex.Message.Contains("EXTERNAL_MEMORY"))
                    {
                        return CreateFallbackTexture(device, renderTarget.Description.Width, renderTarget.Description.Height, format);
                    }
                    throw; // Re-throw if it's not a Wine-related sharing issue
                }
            }
            else
            {
                // Texture was created without shared flag (Wine compatibility mode)
                // Create a fallback D3D9 texture
                return CreateFallbackTexture(device, renderTarget.Description.Width, renderTarget.Description.Height, format);
            }
        }

        private static Texture CreateFallbackTexture(DeviceEx device, int width, int height, Format format)
        {
            try
            {
                // Create a regular D3D9 render target texture (not shared)
                return new Texture(device, width, height, 1, Usage.RenderTarget, format, Pool.Default);
            }
            catch (SharpDX.SharpDXException)
            {
                // If render target creation fails, try without render target usage
                try
                {
                    return new Texture(device, width, height, 1, Usage.None, format, Pool.Managed);
                }
                catch (SharpDX.SharpDXException)
                {
                    // Final fallback: Try with A8R8G8B8 format which is widely supported
                    return new Texture(device, width, height, 1, Usage.None, Format.A8R8G8B8, Pool.Managed);
                }
            }
        }

        public static SharpDX.Direct3D9.Format ToD3D9(this SharpDX.DXGI.Format dxgiformat)
        {
            switch (dxgiformat)
            {
                case SharpDX.DXGI.Format.R10G10B10A2_UNorm:
                    return SharpDX.Direct3D9.Format.A2B10G10R10;
                case SharpDX.DXGI.Format.B8G8R8A8_UNorm:
                    return SharpDX.Direct3D9.Format.A8R8G8B8;
                case SharpDX.DXGI.Format.R16G16B16A16_Float:
                    return SharpDX.Direct3D9.Format.A16B16G16R16F;

                // not sure those one below will work...

                case SharpDX.DXGI.Format.R32G32B32A32_Float:
                    return SharpDX.Direct3D9.Format.A32B32G32R32F;

                case SharpDX.DXGI.Format.R16G16B16A16_UNorm:
                    return SharpDX.Direct3D9.Format.A16B16G16R16;
                case SharpDX.DXGI.Format.R32G32_Float:
                    return SharpDX.Direct3D9.Format.G32R32F;

                case SharpDX.DXGI.Format.R8G8B8A8_UNorm:
                    return SharpDX.Direct3D9.Format.A8R8G8B8;

                case SharpDX.DXGI.Format.R16G16_UNorm:
                    return SharpDX.Direct3D9.Format.G16R16;

                case SharpDX.DXGI.Format.R16G16_Float:
                    return SharpDX.Direct3D9.Format.G16R16F;
                case SharpDX.DXGI.Format.R32_Float:
                    return SharpDX.Direct3D9.Format.R32F;

                case SharpDX.DXGI.Format.R16_Float:
                    return SharpDX.Direct3D9.Format.R16F;

                case SharpDX.DXGI.Format.A8_UNorm:
                    return SharpDX.Direct3D9.Format.A8;
                case SharpDX.DXGI.Format.R8_UNorm:
                    return SharpDX.Direct3D9.Format.L8;

                default:
                    return SharpDX.Direct3D9.Format.Unknown;
            }
        }
    }
}
