﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <!-- This file was generated by Fody. Manual changes to this file will be lost when your project is rebuilt. -->
  <xs:element name="Weavers">
    <xs:complexType>
      <xs:all>
        <xs:element name="PropertyChanged" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:attribute name="InjectOnPropertyNameChanged" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>Used to control if the On_PropertyName_Changed feature is enabled.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="EventInvokerNames" type="xs:string">
              <xs:annotation>
                <xs:documentation>Used to change the name of the method that fires the notify event. This is a string that accepts multiple values in a comma separated form.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="CheckForEquality" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>Used to control if equality checks should be inserted. If false, equality checking will be disabled for the project.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="CheckForEqualityUsingBaseEquals" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>Used to control if equality checks should use the Equals method resolved from the base class.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="UseStaticEqualsFromBase" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>Used to control if equality checks should use the static Equals method resolved from the base class.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
          </xs:complexType>
        </xs:element>
      </xs:all>
      <xs:attribute name="VerifyAssembly" type="xs:boolean">
        <xs:annotation>
          <xs:documentation>'true' to run assembly verification (PEVerify) on the target assembly after all weavers have been executed.</xs:documentation>
        </xs:annotation>
      </xs:attribute>
      <xs:attribute name="VerifyIgnoreCodes" type="xs:string">
        <xs:annotation>
          <xs:documentation>A comma-separated list of error codes that can be safely ignored in assembly verification.</xs:documentation>
        </xs:annotation>
      </xs:attribute>
      <xs:attribute name="GenerateXsd" type="xs:boolean">
        <xs:annotation>
          <xs:documentation>'false' to turn off automatic generation of the XML Schema file.</xs:documentation>
        </xs:annotation>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
</xs:schema>