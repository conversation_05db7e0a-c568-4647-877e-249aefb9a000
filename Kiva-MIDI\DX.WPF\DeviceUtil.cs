﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SharpDX.DXGI;
using Direct3D11 = SharpDX.Direct3D11;
using Direct3D = SharpDX.Direct3D;

namespace Kiva_MIDI
{
	public static class DeviceUtil
	{
		public static SharpDX.Direct3D11.Device Create11(
			Direct3D11.DeviceCreationFlags cFlags = Direct3D11.DeviceCreationFlags.None,
			Direct3D.FeatureLevel minLevel = Direct3D.FeatureLevel.Level_9_1
		)
		{
			using (var dg = new DisposeGroup())
			{
				var level = Direct3D11.Device.GetSupportedFeatureLevel();
				if (level < minLevel)
					return null;

				// Try different device creation approaches for Wine compatibility
				return CreateD3D11DeviceWithFallback(cFlags, level, minLevel);
			}
		}

		private static SharpDX.Direct3D11.Device CreateD3D11DeviceWithFallback(
			Direct3D11.DeviceCreationFlags cFlags,
			Direct3D.FeatureLevel level,
			Direct3D.FeatureLevel minLevel)
		{
			// First try: Hardware with original flags
			try
			{
				return new Direct3D11.Device(Direct3D.DriverType.Hardware, cFlags, level);
			}
			catch (SharpDX.SharpDXException)
			{
				// Wine compatibility fallback 1: Hardware without debug layer
				try
				{
					var fallbackFlags = cFlags & ~Direct3D11.DeviceCreationFlags.Debug;
					return new Direct3D11.Device(Direct3D.DriverType.Hardware, fallbackFlags, level);
				}
				catch (SharpDX.SharpDXException)
				{
					// Wine compatibility fallback 2: WARP (software) renderer
					try
					{
						return new Direct3D11.Device(Direct3D.DriverType.Warp, Direct3D11.DeviceCreationFlags.None, level);
					}
					catch (SharpDX.SharpDXException)
					{
						// Wine compatibility fallback 3: Reference device
						try
						{
							return new Direct3D11.Device(Direct3D.DriverType.Reference, Direct3D11.DeviceCreationFlags.None, level);
						}
						catch (SharpDX.SharpDXException)
						{
							// Final fallback: Try with minimum feature level
							if (level > minLevel)
							{
								try
								{
									return new Direct3D11.Device(Direct3D.DriverType.Hardware, Direct3D11.DeviceCreationFlags.None, minLevel);
								}
								catch (SharpDX.SharpDXException)
								{
									return new Direct3D11.Device(Direct3D.DriverType.Warp, Direct3D11.DeviceCreationFlags.None, minLevel);
								}
							}
							throw; // Re-throw if all fallbacks failed
						}
					}
				}
			}
		}
	}
}
