# Wine Compatibility Fix for Kiva MIDI

## Problem Description
The Kiva MIDI application was crashing under Wine on Linux with an `INVALIDARG` exception during DirectX graphics initialization. The error occurred in the `System.Collections.ListDictionaryInternal` and various graphics-related calls, specifically during D3D9 device creation.

## Root Cause
The crash was caused by Wine's incomplete implementation of DirectX, particularly:
1. **Hardware vertex processing**: Wine may not fully support hardware vertex processing flags
2. **Multithreading flags**: Wine's D3D implementation may have issues with multithreaded device creation
3. **FPU preservation**: Some DirectX flags that work on Windows may cause issues under Wine
4. **Surface locking**: D3DImage surface operations may fail under Wine's implementation

## Solution Overview
The fix implements a comprehensive fallback system for graphics initialization that detects Wine and uses more compatible settings:

### 1. Wine Detection (`D3D9.cs`)
```csharp
private static bool IsRunningUnderWine()
{
    return Environment.GetEnvironmentVariable("WINEPREFIX") != null ||
           Environment.GetEnvironmentVariable("WINEARCH") != null ||
           System.IO.File.Exists("/proc/version");
}
```

### 2. D3D9 Device Creation Fallbacks (`D3D9.cs`)
- **Wine-specific path**: Starts with software vertex processing and minimal flags
- **Windows path**: Tries hardware first, then falls back progressively
- **Multiple fallback levels**: Hardware → Software → Reference device → Minimal flags

### 3. D3D11 Device Creation Fallbacks (`DeviceUtil.cs`)
- **Hardware with debug removal**: Removes debug layer flags that may cause issues
- **WARP renderer**: Falls back to software rendering
- **Reference device**: Uses reference implementation as last resort
- **Feature level fallback**: Tries minimum supported feature level

### 4. Surface Operation Protection (`DXImageSource.cs`)
- **Lock/Unlock protection**: Wraps D3DImage operations in try-catch blocks
- **Surface creation fallback**: Handles cases where surface creation fails
- **Graceful degradation**: Continues operation even if some surface operations fail

### 5. Enhanced Error Messages (`MainWindow.xaml.cs`, `Program.cs`)
- **Wine-specific guidance**: Provides Wine-specific troubleshooting tips
- **Technical details**: Includes underlying exception information
- **Installation suggestions**: Recommends Wine components to install

## Files Modified

1. **`Kiva-MIDI\DX.WPF\D3D9.cs`**
   - Added Wine detection
   - Implemented progressive fallback system for device creation
   - Wine-optimized device creation path

2. **`Kiva-MIDI\DX.WPF\DeviceUtil.cs`**
   - Enhanced D3D11 device creation with multiple fallback options
   - Added support for WARP and Reference renderers

3. **`Kiva-MIDI\DX.WPF\DXImageSource.cs`**
   - Added error handling for Lock/Unlock operations
   - Protected surface creation and manipulation

4. **`Kiva-MIDI\MainWindow.xaml.cs`**
   - Enhanced graphics initialization error handling
   - Wine-specific error messages and troubleshooting guidance

5. **`Kiva-MIDI\Program.cs`**
   - Added Wine compatibility check
   - Preparation for future Wine-specific optimizations

## Testing
A test class `WineCompatibilityTest.cs` was created to verify:
- Wine detection functionality
- D3D9 fallback system
- D3D11 fallback system

## Usage Instructions

### For Wine Users:
1. Ensure you have the latest Wine version
2. Install DirectX components: `winetricks d3dx9 d3dx11_43`
3. Try different Wine configurations if issues persist
4. The application will now automatically use Wine-compatible graphics settings

### For Developers:
- The fallback system is automatic and requires no configuration
- Error messages will indicate if Wine is detected and provide specific guidance
- The system gracefully degrades from hardware to software rendering as needed

## Benefits
- **Automatic compatibility**: No user configuration required
- **Graceful degradation**: Falls back to software rendering if hardware fails
- **Better error messages**: Provides actionable troubleshooting information
- **Maintains performance**: Uses best available graphics options for each platform
- **Future-proof**: Extensible system for additional compatibility fixes

## Technical Notes
- The fallback system tries multiple DirectX device creation approaches
- Wine detection is performed at runtime without external dependencies
- Error handling is comprehensive but doesn't mask underlying issues
- Performance impact is minimal as fallbacks only occur on initialization failure
