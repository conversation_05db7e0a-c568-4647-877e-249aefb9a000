using System;
using System.IO;

namespace Kiva_MIDI.Tests
{
    /// <summary>
    /// Simple test class to verify Wine compatibility improvements
    /// </summary>
    public static class WineCompatibilityTest
    {
        public static void TestWineDetection()
        {
            Console.WriteLine("Testing Wine detection...");
            
            bool isWine = Environment.GetEnvironmentVariable("WINEPREFIX") != null ||
                         Environment.GetEnvironmentVariable("WINEARCH") != null ||
                         File.Exists("/proc/version");
            
            Console.WriteLine($"Wine detected: {isWine}");
            
            if (isWine)
            {
                Console.WriteLine("Wine environment variables:");
                Console.WriteLine($"WINEPREFIX: {Environment.GetEnvironmentVariable("WINEPREFIX")}");
                Console.WriteLine($"WINEARCH: {Environment.GetEnvironmentVariable("WINEARCH")}");
                Console.WriteLine($"/proc/version exists: {File.Exists("/proc/version")}");
            }
        }
        
        public static void TestD3D9Fallback()
        {
            Console.WriteLine("Testing D3D9 device creation with fallbacks...");
            
            try
            {
                var d3d9 = new D3D9();
                Console.WriteLine("D3D9 device created successfully");
                d3d9.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"D3D9 device creation failed: {ex.Message}");
            }
        }
        
        public static void TestD3D11Fallback()
        {
            Console.WriteLine("Testing D3D11 device creation with fallbacks...");
            
            try
            {
                var device = DeviceUtil.Create11();
                if (device != null)
                {
                    Console.WriteLine("D3D11 device created successfully");
                    device.Dispose();
                }
                else
                {
                    Console.WriteLine("D3D11 device creation returned null");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"D3D11 device creation failed: {ex.Message}");
            }
        }
        
        public static void RunAllTests()
        {
            Console.WriteLine("=== Wine Compatibility Tests ===");
            TestWineDetection();
            Console.WriteLine();
            TestD3D9Fallback();
            Console.WriteLine();
            TestD3D11Fallback();
            Console.WriteLine("=== Tests Complete ===");
        }
    }
}
