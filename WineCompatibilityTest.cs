using System;
using System.IO;

namespace Kiva_MIDI.Tests
{
    /// <summary>
    /// Simple test class to verify Wine compatibility improvements
    /// </summary>
    public static class WineCompatibilityTest
    {
        public static void TestWineDetection()
        {
            Console.WriteLine("Testing Wine detection...");

            bool isWine = Environment.GetEnvironmentVariable("WINEPREFIX") != null ||
                         Environment.GetEnvironmentVariable("WINEARCH") != null ||
                         File.Exists("/proc/version");

            Console.WriteLine($"Wine detected: {isWine}");

            if (isWine)
            {
                Console.WriteLine("Wine environment variables:");
                Console.WriteLine($"WINEPREFIX: {Environment.GetEnvironmentVariable("WINEPREFIX")}");
                Console.WriteLine($"WINEARCH: {Environment.GetEnvironmentVariable("WINEARCH")}");
                Console.WriteLine($"/proc/version exists: {File.Exists("/proc/version")}");

                // Test DXVK detection
                bool isDXVK = Environment.GetEnvironmentVariable("DXVK_HUD") != null ||
                             Environment.GetEnvironmentVariable("DXVK_LOG_LEVEL") != null ||
                             File.Exists(Path.Combine(Environment.SystemDirectory, "dxgi.dll"));

                Console.WriteLine($"DXVK detected: {isDXVK}");
                if (isDXVK)
                {
                    Console.WriteLine("DXVK environment variables:");
                    Console.WriteLine($"DXVK_HUD: {Environment.GetEnvironmentVariable("DXVK_HUD")}");
                    Console.WriteLine($"DXVK_LOG_LEVEL: {Environment.GetEnvironmentVariable("DXVK_LOG_LEVEL")}");
                    Console.WriteLine($"dxgi.dll exists: {File.Exists(Path.Combine(Environment.SystemDirectory, "dxgi.dll"))}");
                }
                else
                {
                    Console.WriteLine("WARNING: Wine without DXVK detected. Shader compilation may fail.");
                    Console.WriteLine("Consider installing DXVK: 'winetricks dxvk'");
                }
            }
        }
        
        public static void TestD3D9Fallback()
        {
            Console.WriteLine("Testing D3D9 device creation with fallbacks...");
            
            try
            {
                var d3d9 = new D3D9();
                Console.WriteLine("D3D9 device created successfully");
                d3d9.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"D3D9 device creation failed: {ex.Message}");
            }
        }
        
        public static void TestD3D11Fallback()
        {
            Console.WriteLine("Testing D3D11 device creation with fallbacks...");
            
            try
            {
                var device = DeviceUtil.Create11();
                if (device != null)
                {
                    Console.WriteLine("D3D11 device created successfully");
                    device.Dispose();
                }
                else
                {
                    Console.WriteLine("D3D11 device creation returned null");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"D3D11 device creation failed: {ex.Message}");
            }
        }
        
        public static void TestTextureSharing()
        {
            Console.WriteLine("Testing D3D11/D3D9 texture sharing...");

            try
            {
                var d3d11 = new D3D11();
                var d3d9 = new D3D9();

                Console.WriteLine("D3D11 and D3D9 devices created successfully");

                // Test texture creation and sharing
                d3d11.Reset(256, 256);
                var d3d11Texture = d3d11.RenderTarget;

                Console.WriteLine($"D3D11 texture created: {d3d11Texture.Description.Width}x{d3d11Texture.Description.Height}");
                Console.WriteLine($"Shared flag: {(d3d11Texture.Description.OptionFlags & SharpDX.Direct3D11.ResourceOptionFlags.Shared) != 0}");

                var d3d9Texture = DXSharing.GetSharedD3D9(d3d9.Device, d3d11Texture);
                if (d3d9Texture != null)
                {
                    Console.WriteLine("D3D9 texture created from D3D11 texture successfully");
                    d3d9Texture.Dispose();
                }
                else
                {
                    Console.WriteLine("D3D9 texture creation returned null");
                }

                d3d11.Dispose();
                d3d9.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Texture sharing test failed: {ex.Message}");
            }
        }

        public static void RunAllTests()
        {
            Console.WriteLine("=== Wine Compatibility Tests ===");
            TestWineDetection();
            Console.WriteLine();
            TestD3D9Fallback();
            Console.WriteLine();
            TestD3D11Fallback();
            Console.WriteLine();
            TestTextureSharing();
            Console.WriteLine("=== Tests Complete ===");
        }
    }
}
