﻿<UserControl x:Class="Kiva_MIDI.InfoIcon"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Kiva_MIDI"
             mc:Ignorable="d" >
    <Grid>
        <Grid WindowChrome.IsHitTestVisibleInChrome="True" Width="14" Height="14" Name="grid" Background="Transparent" MouseDown="Grid_MouseDown" HorizontalAlignment="Center" VerticalAlignment="Center">
            <Image Source="infoIcon.png" RenderOptions.BitmapScalingMode="HighQuality"/>
        </Grid>
    </Grid>
</UserControl>
