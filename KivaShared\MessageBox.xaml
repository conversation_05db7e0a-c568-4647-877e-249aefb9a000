﻿<Window x:Class="KivaShared.MessageBox"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:KivaShared"
        mc:Ignorable="d"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="16"
        TextOptions.TextFormattingMode="Ideal" 
        TextOptions.TextRenderingMode="Auto"        
        Title="MessageBox" Loaded="Window_Loaded" MinHeight="100" MinWidth="200" WindowStyle="None" WindowStartupLocation="CenterScreen" HorizontalAlignment="Center" VerticalAlignment="Center" HorizontalContentAlignment="Stretch" VerticalContentAlignment="Stretch" SizeToContent="WidthAndHeight" SizeChanged="Window_SizeChanged">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="ButtonTemplates.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <WindowChrome.WindowChrome>
        <WindowChrome CaptionHeight="{Binding ActualHeight,ElementName=titlebar}"/>
    </WindowChrome.WindowChrome>
    <Grid>
        <DockPanel LastChildFill="True">
            <Border Panel.ZIndex="1" Visibility="{Binding ChromeVisibility, RelativeSource={RelativeSource FindAncestor, AncestorType=Window}}" Background="#0068c9" DockPanel.Dock="Top" Height="40" x:Name="titlebar">
                <Border.Effect>
                    <DropShadowEffect BlurRadius="5" ShadowDepth="0"/>
                </Border.Effect>
                <DockPanel>
                    <DockPanel DockPanel.Dock="Right" Margin="0" Width="40">
                        <Button Style="{StaticResource WindowButton}" Focusable="False" Name="ExitButton" Background="Red" Width="20" Height="20" Margin="3" DockPanel.Dock="Right" WindowChrome.IsHitTestVisibleInChrome="True" Click="ExitButton_Click"></Button>
                    </DockPanel>
                    <Grid>
                        <TextBlock Name="titleText" Margin="10,0" FontSize="16" Foreground="White" HorizontalAlignment="Left" VerticalAlignment="Center">
                        </TextBlock>
                    </Grid>
                </DockPanel>
            </Border>
            <Grid Name="content" Background="#0179db">
                <TextBlock Name="bodyText" FontSize="16" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="10" TextWrapping="Wrap">
                </TextBlock>
            </Grid>
        </DockPanel>
    </Grid>
</Window>
