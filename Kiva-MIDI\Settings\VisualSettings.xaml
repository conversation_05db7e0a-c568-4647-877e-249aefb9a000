﻿<UserControl x:Class="Kiva_MIDI.VisualSettings"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Kiva_MIDI"
             mc:Ignorable="d" 
             d:DesignHeight="350" d:DesignWidth="600">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../ButtonTemplates.xaml"/>
                <ResourceDictionary Source="../Scrollbar.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition/>
            <ColumnDefinition/>
        </Grid.ColumnDefinitions>
        <Border Padding="10">
            <StackPanel>
                <StackPanel>
                    <DockPanel HorizontalAlignment="Left">
                        <Label FontSize="14">Keyboard Key Range</Label>
                        <local:InfoIcon Title="Key Range" Text="The visible key range. The keyboard maintains its aspect ratio when changing size."/>
                    </DockPanel>
                    <local:BetterRadio RadioChecked="RangeChanged" x:Name="key88Range" Height="26" Text="88 Keys"/>
                    <local:BetterRadio RadioChecked="RangeChanged" x:Name="key128Range" Height="26" Text="128 Keys"/>
                    <local:BetterRadio RadioChecked="RangeChanged" x:Name="key256Range" Height="26" Text="256 Keys"/>
                    <DockPanel HorizontalAlignment="Left">
                        <local:BetterRadio RadioChecked="RangeChanged" ParentDepth="2" x:Name="midiRange" Height="26" Text="MIDI's Key Range"/>
                        <local:InfoIcon Title="MIDI Key Range" Text="From the leftmost to the rightmost note in the midi file" Margin="5,0,0,4" VerticalAlignment="Bottom"/>
                    </DockPanel>
                    <DockPanel HorizontalAlignment="Left">
                        <local:BetterRadio RadioChecked="RangeChanged" ParentDepth="2" x:Name="dynamicRange" Height="26" Text="Dynamic 88-128 Range"/>
                        <local:InfoIcon Title="Dynamic Key Range" Text="The keyboard will animate between 128 and 88 keys, depending on what notes are on screen" Margin="5,0,0,4" VerticalAlignment="Bottom"/>
                    </DockPanel>
                    <DockPanel Margin="0">
                        <local:BetterRadio RadioChecked="RangeChanged" x:Name="customRange" ParentDepth="2" Height="26" Text="Custom" Margin="0,0,10,0"/>
                        <local:NumberSelect x:Name="firstKey" Minimum="0" ValueChanged="FirstKey_ValueChanged" Maximum="{Binding ElementName=lastKey, Path=Value}" IsEnabled="{Binding ElementName=customRange, Path=IsChecked}" Width="70" HorizontalAlignment="Left" VerticalAlignment="Bottom"/>
                        <Label FontSize="14">To</Label>
                        <local:NumberSelect x:Name="lastKey" Maximum="256" ValueChanged="LastKey_ValueChanged" Minimum="{Binding ElementName=firstKey, Path=Value}" IsEnabled="{Binding ElementName=customRange, Path=IsChecked}" Width="70" HorizontalAlignment="Left" VerticalAlignment="Bottom"/>
                    </DockPanel>
                </StackPanel>
                <StackPanel>
                    <Label FontSize="14">Keyboard Style</Label>
                    <local:BetterRadio RadioChecked="KBStyleChanged" x:Name="bigKeyboard" Height="26" Text="Big Keyboard"/>
                    <local:BetterRadio RadioChecked="KBStyleChanged" x:Name="smallKeyboard" Height="26" Text="Small Keyboard"/>
                    <local:BetterRadio RadioChecked="KBStyleChanged" x:Name="noKeyboard" Height="26" Text="No Keyboard"/>
                </StackPanel>
                <DockPanel>
                    <Label FontSize="14" HorizontalAlignment="Left">
                        Lock FPS
                    </Label>
                    <local:NumberSelect x:Name="fpsLock" ValueChanged="FpsLock_ValueChanged" Minimum="0" Maximum="10000" HorizontalAlignment="Left" Width="80" VerticalAlignment="Bottom" Margin="0,0,0,1"/>
                    <local:BetterCheckbox x:Name="syncFps" Text="Sync" Margin="10,0,0,0" CheckToggled="syncFps_CheckToggled"/>
                    <local:InfoIcon Title="FPS" Text="The Sync option forces the framerate to be synced to the WPF's render framerate. Very similar to VSync, makes smoother fps. Also setting FPS to 0 makes it unlocked." HorizontalAlignment="Left" Margin="5,0,0,0"/>
                </DockPanel>
            </StackPanel>
        </Border>
        <Border Grid.Column="1" Padding="10">
            <DockPanel>
                <Grid DockPanel.Dock="Top">
                    <DockPanel HorizontalAlignment="Left">
                        <Label FontSize="14" HorizontalAlignment="Left">
                            Note Color Palette
                        </Label>
                        <local:InfoIcon
                            Title="Palettes"
                            Text="Work the same way as Zenith palettes. Image must be a png, either 16 or 32 pixels wide. Every column represents a midi channel, every row represents a track. If the image is 32 pixels wide, then every two rows represent a channel making the notes have gradients."
                            />
                    </DockPanel>
                    <Button Name="reloadPalettes" PreviewMouseDown="ReloadPalettes_PreviewMouseDown" Style="{DynamicResource TransparentButtonStyle}" HorizontalAlignment="Right" Width="80" FontSize="14" Margin="0,3">
                        Reload
                    </Button>
                </Grid>
                <Button Name="openPaletteFolder" PreviewMouseDown="OpenPaletteFolder_PreviewMouseDown" DockPanel.Dock="Bottom" Style="{DynamicResource TransparentButtonStyle}" HorizontalAlignment="Center" Width="140" Height="25" FontSize="14" Margin="0,3,0,0">
                    Open Palette Folder
                </Button>
                <DockPanel DockPanel.Dock="Bottom" Margin="0,5,0,0">
                    <local:BetterCheckbox x:Name="randomisePaletteOrder" CheckToggled="RandomisePaletteOrder_CheckToggled" Text="Randomise Color Order" HorizontalAlignment="Left"/>
                    <local:InfoIcon HorizontalAlignment="Left" VerticalAlignment="Bottom" Margin="5,0,0,2"
                                    Text="Shuffle the palette colors"
                                    Title="Randomise color order"
                                    />
                </DockPanel>
                <Border BorderThickness="1" BorderBrush="Black">
                    <ScrollViewer Name="paletteList" VerticalScrollBarVisibility="Auto">
                        <StackPanel Name="palettesPanel" VerticalAlignment="Top">
                        </StackPanel>
                    </ScrollViewer>
                </Border>
                <!--<local:BetterCheckbox CheckToggled="CompatibilityFps_CheckToggled" x:Name="compatibilityFps" Text="Compatibility FPS"/>-->
            </DockPanel>
        </Border>
    </Grid>
</UserControl>
